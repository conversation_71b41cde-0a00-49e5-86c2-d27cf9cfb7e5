#!/bin/bash
set -euo pipefail
IFS=$'\n\t'

# 原：
# BASE_PATH="/mnt/efs/production/bureau"
# DOCKER_PATH="${BASE_PATH}/medical-docker"

# 建议改成「只在未定义时赋默认值」：
BASE_PATH="${BASE_PATH:-/mnt/efs/production/bureau}"
DOCKER_PATH="${DOCKER_PATH:-${BASE_PATH}/medical-docker}"

# === 进入指定的项目目录 ===
into_directory() {
  local project="$1"
  local target_path="${BASE_PATH}/${project}"

  if [ -d "$target_path" ]; then
    cd "$target_path" || exit 1
  else
    echo "[ERROR] Directory not found: $target_path"
    exit 1
  fi
}

# === 检出指定 Git 标签 ===
checkout_tag() {
  local git_tag="$1"
  echo "[INFO] Fetching Git tags..."
  git fetch --tags

  echo "[INFO] Checking out tag: $git_tag"
  if git rev-parse --verify "$git_tag" >/dev/null 2>&1; then
    git checkout "$git_tag"
  else
    git checkout -b "$git_tag" "refs/tags/$git_tag"
  fi
}

# === 比较 composer 文件是否有变化 ===
diff_composer() {
  local base_commit="$1"

  if git diff --quiet "$base_commit" HEAD -- composer.json composer.lock; then
    echo "[INFO] No changes in composer.json or composer.lock"
    return 1
  else
    echo "[INFO] Changes detected in composer.json or composer.lock"
    return 0
  fi
}

# === 在容器中执行 composer install ===
update_composer() {
  local project_name="$1"
  echo "[INFO] Updating composer dependencies for: $project_name"

  cd "$DOCKER_PATH" || exit 1

  if docker-compose exec -T php-fpm-74 bash -c "
    set -e
    cd /data/www/${project_name}
    composer install --no-interaction --optimize-autoloader
  "; then
    echo "[INFO] Composer install completed successfully"
  else
    local exit_code=$?
    echo "[WARN] Composer install returned exit code: $exit_code"
    if docker-compose exec -T php-fpm-74 bash -c "
      cd /data/www/${project_name}
      [ -d vendor ] && [ -f vendor/autoload.php ]
    "; then
      echo "[INFO] Composer dependencies appear to be installed correctly despite warnings"
      echo "[INFO] This is likely due to deprecation warnings which are non-fatal"
    else
      echo "[ERROR] Composer install genuinely failed - vendor directory or autoload.php missing"
      exit $exit_code
    fi
  fi
}

# === 在容器中执行 add poll interval list（添加多设备共享功能名单）===
add_poll_interval() {
  local project_name="$1"
  local merchant_id="$2"
  local interval_ms="$3"

  cd "$DOCKER_PATH" || exit 1

  if docker-compose exec -T php-fpm-74 bash -c "
    set -e
    cd /data/www/${project_name}
    php artisan merchant:passive-poll-interval --action=add --merchantId=${merchant_id} --value=${interval_ms}
  "; then
    echo "[INFO] Add poll interval completed successfully"
  else
    local exit_code=$?
    echo "[ERROR] Add poll interval failed with exit code: $exit_code"
    exit $exit_code
  fi
}

  # cd /mnt/efs/production/www/medical-docker && /usr/local/bin/docker-compose 
  # --env-file /mnt/efs/production/www/medical-docker/.env 
  # exec -T --user=www-data --workdir=/data/www/neox-med-backend  php-fpm-74  
  # php artisan merchant:passive-poll-interval --action=add --merchantId={替换成你想设置的店铺ID} --value=10000


# === 在容器中执行 list poll interval（展示多设备共享功能名单）===
list_poll_interval() {
  local project_name="$1"

  cd "$DOCKER_PATH" || exit 1

  if docker-compose exec -T php-fpm-74 bash -c "
    set -e
    cd /data/www/${project_name}
    php artisan merchant:passive-poll-interval --action=list
  "; then
    echo "[INFO] Add poll interval completed successfully"
  else
    local exit_code=$?
    echo "[ERROR] Add poll interval failed with exit code: $exit_code"
    exit $exit_code
  fi
}
